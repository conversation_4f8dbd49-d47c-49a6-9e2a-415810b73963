-- Migration: Add 'company_deleted' event type to activity log
-- This migration adds support for logging company deletion events

-- Drop the existing check constraint
ALTER TABLE activity_log DROP CONSTRAINT IF EXISTS activity_log_event_type_check;

-- Add the updated constraint with company_deleted event type (maintaining alphabetical order)
ALTER TABLE activity_log ADD CONSTRAINT activity_log_event_type_check 
CHECK (event_type IN (
  'benefit_automatically_removed',
  'benefit_disputed',
  'benefit_removal_dispute_approved',
  'benefit_removal_dispute_cancelled',
  'benefit_removal_dispute_rejected',
  'benefit_removal_dispute_submitted',
  'benefit_verified',
  'cache_refresh',
  'company_added',
  'company_deleted',
  'session_cleanup',
  'user_deleted',
  'user_registered'
));

-- Update the comment to reflect all event types including company_deleted
COMMENT ON COLUMN activity_log.event_type IS 'Type of event: benefit_automatically_removed, benefit_disputed, benefit_removal_dispute_approved, benefit_removal_dispute_cancelled, benefit_removal_dispute_rejected, benefit_removal_dispute_submitted, benefit_verified, cache_refresh, company_added, company_deleted, session_cleanup, user_deleted, user_registered';

-- Log this migration
INSERT INTO migration_log (migration_name, description) 
VALUES ('023-add-company-deleted-event-type', 'Add company_deleted event type to activity log for tracking admin company deletions');
